# 当贝AI聊天界面使用指南

## 📖 概述

当贝AI聊天界面是一个现代化的Web应用，为当贝AI Provider项目提供了直观、易用的聊天交互界面。支持多模型对话、流式响应、会话管理等丰富功能。

## ✨ 功能特性

### 🤖 核心功能
- **多模型支持** - 支持多种AI模型，可动态切换
- **流式响应** - 实时显示AI回复，支持打字机效果
- **会话管理** - 创建、删除、切换多个对话会话
- **消息历史** - 本地存储对话历史，支持导入导出
- **Markdown渲染** - 完整支持Markdown格式，包括代码高亮

### 🎨 界面特性
- **响应式设计** - 完美适配桌面和移动设备
- **深色/浅色主题** - 支持主题切换，跟随系统设置
- **现代化UI** - 简洁美观的界面设计
- **无障碍访问** - 支持键盘导航和屏幕阅读器

### ⚙️ 高级功能
- **思考模式** - 支持模型深度思考分析
- **联网搜索** - 获取最新信息和实时数据
- **快捷键支持** - 提高操作效率
- **错误处理** - 完善的错误提示和重试机制

## 🚀 快速开始

### 1. 启动服务器

```bash
# 开发模式
npm run server:dev

# 生产模式
npm run server:prod

# 指定端口
npm run server -- --port 8080
```

### 2. 访问界面

打开浏览器访问：
- **聊天界面**: http://localhost:3000/chat
- **API测试工具**: http://localhost:3000/api-tester

### 3. 开始对话

1. 选择一个AI模型
2. 在输入框中输入您的问题
3. 按Enter发送消息或点击发送按钮
4. 享受与AI的智能对话

## 📱 界面介绍

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏: Logo + 主题切换 + 设置                        │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────┐ │
│ │   侧边栏     │ │           聊天区域                   │ │
│ │             │ │                                     │ │
│ │ • 新建对话   │ │ ┌─────────────────────────────────┐ │ │
│ │ • 模型选择   │ │ │        消息显示区域              │ │ │
│ │ • 对话选项   │ │ │                                 │ │ │
│ │ • 历史会话   │ │ └─────────────────────────────────┘ │ │
│ │             │ │ ┌─────────────────────────────────┐ │ │
│ │             │ │ │        消息输入区域              │ │ │
│ └─────────────┘ └─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 侧边栏功能

- **新建对话**: 创建新的聊天会话
- **模型选择**: 选择不同的AI模型
- **对话选项**: 
  - 思考模式：启用深度思考分析
  - 联网搜索：获取最新信息
- **历史会话**: 查看和切换历史对话

### 聊天区域

- **消息显示**: 显示用户和AI的对话内容
- **消息输入**: 支持多行输入，自动调整高度
- **发送控制**: 发送消息或停止生成

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Enter` | 发送消息 |
| `Shift + Enter` | 换行 |
| `Ctrl + N` | 新建对话 |
| `Ctrl + D` | 切换深色模式 |
| `Ctrl + /` | 显示快捷键帮助 |
| `Esc` | 停止生成 |

## 🔧 配置选项

### 主题设置

支持三种主题模式：
- **浅色模式**: 适合白天使用
- **深色模式**: 适合夜间使用
- **跟随系统**: 自动跟随系统设置

### 对话设置

- **自动滚动**: 自动滚动到最新消息
- **消息提示音**: 新消息提示音效
- **思考模式**: 默认启用深度思考
- **联网搜索**: 默认启用网络搜索

## 📊 数据管理

### 本地存储

所有对话数据存储在浏览器本地，包括：
- 对话会话列表
- 消息历史记录
- 用户设置偏好

### 导入导出

- **导出对话**: 将当前对话导出为JSON文件
- **导入对话**: 从JSON文件导入对话历史
- **清空数据**: 清除所有本地数据

### 存储限制

- 最大会话数量: 100个
- 每个会话最大消息数: 1000条
- 自动清理旧数据以节省空间

## 🔍 故障排除

### 常见问题

**Q: 无法加载模型列表**
A: 检查网络连接和服务器状态，确保API服务正常运行

**Q: 消息发送失败**
A: 检查网络连接，确认选择了有效的模型

**Q: 流式响应中断**
A: 网络不稳定可能导致连接中断，点击重试或刷新页面

**Q: 界面显示异常**
A: 清除浏览器缓存，或尝试无痕模式

### 调试模式

在浏览器控制台中输入以下命令启用调试：

```javascript
// 启用详细日志
localStorage.setItem('debug', 'true');

// 查看存储使用情况
console.log(storage.getStorageUsage());

// 导出所有数据
console.log(storage.exportData());
```

## 🌐 浏览器兼容性

### 支持的浏览器

- **Chrome** 80+
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

### 必需功能

- ES6+ JavaScript支持
- Fetch API
- LocalStorage
- EventSource (SSE)

## 📱 移动端使用

### 响应式适配

- 自动适配不同屏幕尺寸
- 触摸友好的交互设计
- 移动端优化的侧边栏

### 移动端特性

- 侧边栏改为抽屉式设计
- 优化的触摸目标大小
- 适配虚拟键盘

## 🔒 隐私安全

### 数据安全

- 所有数据存储在本地浏览器
- 不会上传个人对话内容
- 支持完全离线使用

### 隐私保护

- 无用户追踪
- 无数据收集
- 开源透明

## 🛠️ 开发部署

### 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run server:dev

# 构建生产版本
npm run build
```

### 生产部署

```bash
# 构建项目
npm run build

# 启动生产服务器
npm run server:prod
```

### 环境变量

```bash
# 服务器端口
PORT=3000

# 调试模式
DEBUG=true

# CORS设置
CORS_ORIGIN=*
```

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. 查看本文档的故障排除部分
2. 检查浏览器控制台的错误信息
3. 提交Issue到项目仓库
4. 联系技术支持团队

---

**当贝AI聊天界面** - 让AI对话更简单、更智能、更高效！
