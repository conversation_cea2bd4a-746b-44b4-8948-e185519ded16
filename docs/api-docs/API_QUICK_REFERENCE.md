# 当贝AI Provider API 快速参考

## 基础信息

- **基础URL**: `http://localhost:3000`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 端点概览

### 系统端点
| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |
| GET | `/stats` | 使用统计 |
| GET | `/api/info` | API信息 |

### 模型端点
| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/models` | 获取所有模型 |
| GET | `/api/models/recommended` | 获取推荐模型 |
| GET | `/api/models/:modelId` | 获取特定模型 |
| POST | `/api/models/reload` | 重新加载模型数据 |

### 聊天端点
| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/chat` | 聊天对话（支持流式） |

### 文本生成端点
| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/text/generate` | 文本生成（支持流式） |
| GET | `/api/text/models` | 文本生成模型列表 |

### OpenAI兼容端点
| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/v1/models` | 模型列表（OpenAI格式） |
| POST | `/v1/chat/completions` | 聊天对话（OpenAI格式） |

## 快速示例

### 1. 获取模型列表
```bash
curl -X GET http://localhost:3000/api/models
```

### 2. 发送聊天消息
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "你好"}],
    "model": "deepseek",
    "stream": false
  }'
```

### 3. 生成文本
```bash
curl -X POST http://localhost:3000/api/text/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "请写一首诗",
    "model": "deepseek",
    "task_type": "creative"
  }'
```

### 4. OpenAI兼容调用
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek",
    "messages": [{"role": "user", "content": "Hello"}],
    "stream": false
  }'
```

## 常用参数

### 聊天请求参数
- `messages` (必需): 消息数组
- `model` (必需): 模型ID
- `stream` (可选): 是否流式响应，默认false
- `conversation_id` (可选): 对话ID
- `options` (可选): 模型选项配置

### 文本生成参数
- `prompt` (必需): 生成提示词
- `model` (可选): 模型ID，默认deepseek
- `task_type` (可选): 任务类型
- `stream` (可选): 是否流式响应
- `max_tokens` (可选): 最大生成长度
- `temperature` (可选): 生成温度

## 支持的模型

主要模型列表：
- `deepseek` - DeepSeek-R1最新版（推荐）
- `doubao-1_6-thinking` - 豆包思维版
- `glm-4-5` - 智谱GLM-4.5
- `qwen-plus` - 通义千问Plus
- `gpt-4o` - GPT-4o

## 任务类型

文本生成支持的任务类型：
- `creative` - 创意写作
- `code` - 代码生成
- `document` - 文档生成
- `summary` - 摘要生成
- `translation` - 翻译
- `rewrite` - 改写
- `qa` - 问答
- `general` - 通用生成

## 错误代码

常见错误代码：
- `INVALID_REQUEST` - 请求参数无效
- `UNSUPPORTED_MODEL` - 不支持的模型
- `MODEL_NOT_FOUND` - 模型不存在
- `TIMEOUT_ERROR` - 请求超时
- `CHAT_ERROR` - 聊天处理失败
- `GENERATION_ERROR` - 文本生成失败

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": { /* 响应数据 */ },
  "requestId": "req_123456",
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细信息"
  },
  "requestId": "req_123456",
  "timestamp": 1640995200000
}
```

## 流式响应

流式响应使用 Server-Sent Events (SSE) 格式：

```
Content-Type: text/event-stream

data: {"id":"msg_123","choices":[{"delta":{"content":"你好"}}]}

data: [DONE]
```

## 性能提示

- 模型列表接口有5分钟缓存
- 流式响应适合长文本生成
- 并发请求建议控制在合理范围内
- 大请求体限制为10MB

## 更多信息

详细文档请参考：
- [完整API文档](./HTTP_API_README.md)
- [开发指南](./development.md)
- [部署指南](../README.md)
